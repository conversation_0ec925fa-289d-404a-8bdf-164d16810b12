import { createRedisStorage } from '~/server/utils/redis'

export default defineEventHandler(async (event) => {
	// get all cache keys with pattern matching
	try {
		const query = getQuery(event)
		const pattern = query.pattern as string || '*'

		const redis = createRedisStorage()

		const keys = await redis.getKeys(pattern)

		return {
			message: 'Cache keys retrieved',
			pattern,
			count: keys.length,
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	}
})
