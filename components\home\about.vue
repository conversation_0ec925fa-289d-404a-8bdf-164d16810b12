<script setup lang="ts">
const list = [
	{
		title: 'home.about-stores-title',
		text: 'home.about-stores-text',
		icon: 'ui:stores',
	},

	{
		title: 'home.about-employees-title',
		text: 'home.about-employees-text',
		icon: 'ui:employees',
	},

	{
		title: 'home.about-products-title',
		text: 'home.about-products-text',
		icon: 'ui:products',
	},
]
</script>

<template>
	<div class="col-span-3 w-full rounded-lg bg-white p-4">
		<div class="flex flex-col w-full gap-4">
			<h2 class="text-2xl font-semibold text-gray-700">
				{{ $t('home.about-title') }}
			</h2>
			<p class="text-xl font-normal text-gray-600 leading-10">
				{{
					$t('home.about-text')
				}}
			</p>

			<div class="grid grid-cols-3 w-full gap-4 max-md:grid-cols-1">
				<div
					v-for="(item, index) in list"
					:key="index"
					class="flex gap-2 items-center bg-sky-50 py-2 px-2 rounded-lg"
				>
					<div class="flex p-3 rounded-lg shadow-lg bg-primary-600 text-white">
						<Icon
							:name="item.icon"
							size="35px"
							class="max-w-9"
							loading="lazy"
						/>
					</div>
					<div class="flex flex-col flex-grow">
						<h3 class="text-lg text-gray-800 font-semibold">
							{{ $t(item.title) }}
						</h3>
						<p class="text-sm text-gray-500 font-normal">
							{{ $t(item.text) }}
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">

</style>
