// import { createStorage } from 'unstorage'
// import redisDriver from 'unstorage/drivers/redis'
import Redis from 'ioredis'

export default defineEventHandler(async () => {
	try {
		const redis = new Redis.Cluster([
			{
				host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
				port: 6379,
			},
		])

		const nodes = redis.nodes('master') // get master nodes

		const keys: string[] = []

		for (const node of nodes) {
			let cursor = '0'
			do {
				const [nextCursor, foundKeys] = await node.scan(cursor, 'MATCH', '*', 'COUNT', 100)
				cursor = nextCursor

				if (foundKeys.length > 0) {
					// console.log(`Deleting keys:`, foundKeys)
					// await node.del(...foundKeys)
					keys.push(...foundKeys)
				}
			} while (cursor !== '0')
		}

		return {
			message: 'Cache keys',
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	}
	// get all cache keys
	/* try {
		const redis = createStorage({
			driver: redisDriver({
				base: '{unstorage}',

				cluster: [
					{
						host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
						port: 6379,
					},
				],
				clusterOptions: {

					redisOptions: {

						tls: {
							servername: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
						},
					},
				},

			}),
		})

		const keys = await redis.getKeys('#')

		return {
			message: 'Cache keys',
			keys,
		}
	} catch (error) {
		return {
			message: 'Failed to get cache keys',
			error: `${error}`,
		}
	} */
})
