<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'
import type { User } from '~/interfaces/auth/auth'
import { useCategoriesStore } from '~/store/categoriesStore'

import { useAuthStore } from '~/store/useAuthStore.client'

const categoryStore = useCategoriesStore()
await categoryStore.fetch()
const isDesktop = useMediaQuery('(min-width: 600px)')
const authStore = useAuthStore()
const i18nHead = useLocaleHead()
const route = useRoute()
const router = useRouter()
const drawer = computed(() => route.query?.drawer as string)

useHead(() => ({
	htmlAttrs: {
		lang: i18nHead.value.htmlAttrs!.lang,
		dir: i18nHead.value.htmlAttrs!.dir,
	},
	link: [...(i18nHead.value.link || [])],
	meta: [...(i18nHead.value.meta || [])],
}))

/** Handle on set wish list drawer */
const closeDrawer = (): void => {
	const query = { ...route.query }
	delete query.drawer
	router.replace({
		path: route.path,
		query,
	})
}

onBeforeMount(async () => {
	const { data: userData } = await useApi<User>('/my/profile')
	if (userData?.value) {
		authStore.user = userData?.value
	}
})
</script>

<template>
	<div class="layout">
		<AppHeader />

		<div class="container overflow-hidden">
			<slot />
		</div>

		<LazyAppFooter hydrate-on-visible />
	</div>
	<ClientOnly>
		<LazyDrawerWishList
			:is-open="drawer === 'wishlist'"
			hydrate-on-idle
			@set:wish-list="closeDrawer"
		/>

		<LazyDrawerCartList
			:is-open="drawer === 'cart'"
			hydrate-on-idle
			@set:cart-list="closeDrawer"
		/>
	</ClientOnly>

	<ClientOnly>
		<LazyAuth hydrate-on-idle />
	</ClientOnly>

	<ClientOnly>
		<LazyToaster
			rich-colors
			:toast-options="{
				duration: 8000,

			}"
			:position="isDesktop?'bottom-right':'top-center'"
			hydrate-on-idle
		/>
	</ClientOnly>
</template>

<style lang="scss" scoped>
.layout {
  @apply w-full min-h-dvh flex flex-col items-center justify-center bg-body antialiased;
}
</style>
